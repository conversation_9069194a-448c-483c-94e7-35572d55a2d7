using Core.Results;
using System.Text.Json;

namespace TestPhccNotifier.Core.Results;

public class APIErrorResultTests
{
    [Fact]
    public void DefaultConstructor_ShouldSetDefaultValues()
    {
        // Act
        var result = new APIErrorResult();

        // Assert
        result.ErrorType.Should().Be((int)APIErrorType.None);
        result.ErrorTypeDescription.Should().Be("None");
        result.Message.Should().BeNull();
        result.StackTrace.Should().BeNull();
        result.ValidationErrors.Should().BeNull();
    }

    [Theory]
    [InlineData(APIErrorType.None, 0)]
    [InlineData(APIErrorType.Authentication, 1)]
    [InlineData(APIErrorType.BusinessError, 2)]
    [InlineData(APIErrorType.ValidationError, 3)]
    [InlineData(APIErrorType.SystemError, 4)]
    public void Constructor_WithErrorType_ShouldSetErrorTypeAndDescription(APIErrorType errorType, int expectedValue)
    {
        // Act
        var result = new APIErrorResult(errorType);

        // Assert
        result.ErrorType.Should().Be(expectedValue);
        result.ErrorTypeDescription.Should().Be(errorType.ToString());
    }

    [Fact]
    public void Constructor_WithErrorTypeAndMessage_ShouldSetBoth()
    {
        // Arrange
        const string testMessage = "Test error message";

        // Act
        var result = new APIErrorResult(APIErrorType.BusinessError, testMessage);

        // Assert
        result.ErrorType.Should().Be((int)APIErrorType.BusinessError);
        result.ErrorTypeDescription.Should().Be("BusinessError");
        result.Message.Should().Be(testMessage);
    }

    [Fact]
    public void Constructor_WithErrorTypeMessageAndStackTrace_ShouldSetAll()
    {
        // Arrange
        const string testMessage = "Test error message";
        const string testStackTrace = "Stack trace information";

        // Act
        var result = new APIErrorResult(APIErrorType.SystemError, testMessage, testStackTrace);

        // Assert
        result.ErrorType.Should().Be((int)APIErrorType.SystemError);
        result.ErrorTypeDescription.Should().Be("SystemError");
        result.Message.Should().Be(testMessage);
        result.StackTrace.Should().Be(testStackTrace);
    }

    [Fact]
    public void Constructor_WithValidationErrors_ShouldSetValidationErrors()
    {
        // Arrange
        const string testMessage = "Validation failed";
        var validationErrors = new Dictionary<string, IEnumerable<string>>
        {
            { "Field1", new[] { "Error 1", "Error 2" } },
            { "Field2", new[] { "Error 3" } }
        };

        // Act
        var result = new APIErrorResult(APIErrorType.ValidationError, testMessage, validationErrors);

        // Assert
        result.ErrorType.Should().Be((int)APIErrorType.ValidationError);
        result.ErrorTypeDescription.Should().Be("ValidationError");
        result.Message.Should().Be(testMessage);
        result.ValidationErrors.Should().BeEquivalentTo(validationErrors);
    }

    [Fact]
    public void Constructor_WithEmptyValidationErrors_ShouldSetEmptyDictionary()
    {
        // Arrange
        var emptyValidationErrors = new Dictionary<string, IEnumerable<string>>();

        // Act
        var result = new APIErrorResult(APIErrorType.ValidationError, "Test", emptyValidationErrors);

        // Assert
        result.ValidationErrors.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_WithNullValidationErrors_ShouldSetNull()
    {
        // Act
        var result = new APIErrorResult(APIErrorType.ValidationError, "Test", null);

        // Assert
        result.ValidationErrors.Should().BeNull();
    }

    [Fact]
    public void Serialization_WithNullValues_ShouldIgnoreNullProperties()
    {
        // Arrange
        var result = new APIErrorResult(APIErrorType.BusinessError);
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = JsonSerializer.Serialize(result, options);

        // Assert
        json.Should().Contain("\"errorType\":2");
        json.Should().Contain("\"errorTypeDescription\":\"BusinessError\"");
        json.Should().NotContain("message");
        json.Should().NotContain("stackTrace");
        json.Should().NotContain("validationErrors");
    }

    [Fact]
    public void Serialization_WithAllProperties_ShouldIncludeAllProperties()
    {
        // Arrange
        var validationErrors = new Dictionary<string, IEnumerable<string>>
        {
            { "Field1", new[] { "Error 1" } }
        };
        var result = new APIErrorResult(APIErrorType.ValidationError, "Test message", validationErrors);
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = JsonSerializer.Serialize(result, options);

        // Assert
        json.Should().Contain("\"errorType\":3");
        json.Should().Contain("\"errorTypeDescription\":\"ValidationError\"");
        json.Should().Contain("\"message\":\"Test message\"");
        json.Should().Contain("\"validationErrors\"");
    }

    [Fact]
    public void APIErrorResult_ShouldImplementIAPIErrorResult()
    {
        // Arrange
        var result = new APIErrorResult();

        // Assert
        result.Should().BeAssignableTo<IAPIErrorResult>();
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="IIS Express">WebAPI/WebAPI.csproj</projectFile>
    <projectFile profileName="WebAPI">WebAPI/WebAPI.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ae4612d3-4b23-46aa-bc88-eab1a4312383" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Rebase.Settings">
    <option name="NEW_BASE" value="main" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {}
}]]></component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="add-optional-procedure-details-3655-17-07-2025" />
                    <option name="lastUsedInstant" value="1752742340" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1752740985" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="documentation-branch" />
                    <option name="lastUsedInstant" value="1752737562" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="US3655-add-optional-procedure-details-" />
                    <option name="lastUsedInstant" value="1752666178" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="file-has-been-updated-to-reflect-the-project-changes-12-01-2025" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GithubDefaultAccount">
    <option name="defaultAccountId" value="5788f9a8-168b-4be3-8309-2d64963b27ab" />
  </component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/public-health-care-center-CORP/CancerScreeningSMS.git",
    "accountId": "5788f9a8-168b-4be3-8309-2d64963b27ab"
  },
  "recentNewPullRequestHead": {
    "server": {
      "useHttp": false,
      "host": "github.com",
      "port": null,
      "suffix": null
    },
    "owner": "public-health-care-center-CORP",
    "repository": "CancerScreeningSMS"
  }
}]]></component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/DecompilerCache/decompiler/3c437dedd380408f8549a161f3596802abbf0/1f/f88bb75a/LinkEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/DecompilerCache/decompiler/3c437dedd380408f8549a161f3596802abbf0/47/a563c9a6/QueryExpression.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/DecompilerCache/decompiler/3c437dedd380408f8549a161f3596802abbf0/49/24df13d1/ConditionOperator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/DecompilerCache/decompiler/469a91b9ae754c70be8bf94be2099af3629c0/a1/8b39458d/ServiceClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/DecompilerCache/decompiler/ec8db41afca44caf9b9ef8eaf995f49d6be20/f5/df8e3da8/ServiceClient.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/SourcesCache/6cc6dede52aab6345bbde5d36881fd55b017f53c84c78824d182f9a62c8291f/ISender.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/JetBrains/Shared/vAny/SourcesCache/6f90c6c13479b8c2a5be98f3d75dfc3bd885a055652d8a32904ca2448132949e/Future.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Configurations/UserConfig.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Constants/ActivityStatus.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Core/Constants/MessageStatus.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Dynamics/EntityExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Exceptions/AuthenticationException.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Exceptions/BusinessException.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Exceptions/BusinessException.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Results/APIErrorResult.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Core/Results/APISuccessResult.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/CancelAppointment/CancelAppointmentHandler.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Domain/Features/Message/Commands/NewAppointment/NewAppointmentHandler.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/NewAppointment/NewAppointmentHandler.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/NewAppointment/NewAppointmentRequest.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Domain/Features/Message/Commands/NewAppointment/NewAppointmentRequest.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/NewAppointment/NewAppointmentRequestValidator.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/NewAppointment/NewAppointmentResponse.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/RescheduleAppointment/RescheduleAppointmentHandler.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/RescheduleAppointment/RescheduleAppointmentRequest.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/Domain/Features/Message/Commands/RescheduleAppointment/RescheduleAppointmentValidator.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Domain/Features/Message/MessageManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Domain/Features/Message/Queries/GetAppointmentDetailById/GetAppointmentDetailByIdRequestValidator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/WebAPI/Controllers/AppointmentController.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/WebAPI/Filters/AuthorizeFilter.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/WebAPI/Filters/AuthorizeFilter.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/CancerScreeningSMS/WebAPI/Middlewares/SerilogLogContextMiddleware.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="false" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2p3YCHrxnwgv0zP5hHEBV7fzApC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.WebAPI.executor": "Debug",
    ".NET Launch Settings Profile.WebAPI: IIS Express.executor": "Debug",
    "RunOnceActivity.ChatTagsLlmMigration": "true",
    "RunOnceActivity.ClientConfigCleanupMigration": "true",
    "RunOnceActivity.CodyAccountHistoryMigration": "true",
    "RunOnceActivity.CodyAccountsIdsRefresh": "true",
    "RunOnceActivity.CodyAssignOrphanedChatsToActiveAccount": "true",
    "RunOnceActivity.CodyConvertUrlToCodebaseName": "true",
    "RunOnceActivity.CodyHistoryLlmMigration": "true",
    "RunOnceActivity.CodyMigrateChatHistory-v2": "true",
    "RunOnceActivity.CodyProjectSettingsMigration": "true",
    "RunOnceActivity.DeprecatedChatLlmMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.ToggleCodyToolWindowAfterMigration": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SONARLINT_PRECOMMIT_ANALYSIS": "true",
    "com.codeium.enabled": "true",
    "git-widget-placeholder": "add-optional-procedure-details-3655-17-07-2025",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "org.jetbrains.plugins.github.ui.GithubSettingsConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.WebAPI: IIS Express">
    <configuration name="WebAPI" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/WebAPI/WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="WebAPI" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WebAPI: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/WebAPI/WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ae4612d3-4b23-46aa-bc88-eab1a4312383" name="Changes" comment="" />
      <created>1731996100888</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1731996100888</updated>
      <workItem from="1731996109056" duration="9047000" />
      <workItem from="1736676372445" duration="3951000" />
      <workItem from="1737265841633" duration="311000" />
      <workItem from="1737266453260" duration="800000" />
      <workItem from="1737267335696" duration="4001000" />
      <workItem from="1737286127872" duration="1965000" />
      <workItem from="1737347007808" duration="4450000" />
      <workItem from="1737354406723" duration="9392000" />
      <workItem from="1737525556265" duration="1932000" />
      <workItem from="1737530093320" duration="12145000" />
      <workItem from="1737865505128" duration="43000" />
      <workItem from="1737869314107" duration="638000" />
      <workItem from="1737870093291" duration="6318000" />
      <workItem from="1737951214482" duration="1157000" />
      <workItem from="1738041069336" duration="236000" />
      <workItem from="1739791172980" duration="220000" />
      <workItem from="1741592358456" duration="1877000" />
      <workItem from="1744090640751" duration="114000" />
      <workItem from="1752663345666" duration="387000" />
      <workItem from="1752664628274" duration="2179000" />
      <workItem from="1752734173245" duration="8895000" />
    </task>
    <task id="LOCAL-00001" summary="feat: Refactor appointment controller and add service&#10;&#10;Refactor AppointmentController to use services instead of &#10;MediatR.  Update dependencies to net8.0. Add  &#10;SecurityHeadersMiddleware to improve security. Add model &#10;validation filter.  Improve configuration handling for &#10;users. (18-11-2024)">
      <option name="closed" value="true" />
      <created>1731998225526</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1731998225526</updated>
    </task>
    <task id="LOCAL-00002" summary="Add Dynamics integration with repository and service layers&#10;&#10;- Introduce `DynamicsConfig` class for configuration management.&#10;- Implement `DynamicsAppointmentRepository`, `DynamicsLocationRepository`, and `DynamicsPatientRepository` for data access.&#10;- Define repository interfaces: `IAppointmentRepository`, `ILocationRepository`, and `IPatientRepository`.&#10;- Add `RepositoryServiceExtensions` for dependency injection setup.&#10;- Create `ServiceCollectionExtensions` to configure Dynamics client and repositories.&#10;- Develop `AppointmentService` in `Domain.Services` for appointment management.&#10;- Add domain models for appointment requests and responses.">
      <option name="closed" value="true" />
      <created>1731998342121</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1731998342121</updated>
    </task>
    <task id="LOCAL-00003" summary="Add query to filter by cancer screening in DynamicsLocationRepository and update Dynamics config settings">
      <option name="closed" value="true" />
      <created>1731998438616</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1731998438616</updated>
    </task>
    <task id="LOCAL-00004" summary="Refactor DynamicsLocationRepository to use ServiceClient for data retrieval">
      <option name="closed" value="true" />
      <created>1731998483704</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1731998483704</updated>
    </task>
    <task id="LOCAL-00005" summary="- **Package Updates:** All project's package references have been updated to their latest available versions.&#10;- **.NET SDK Enforcement:** A `global.json` file has been added to the root directory to enforce the use of the .NET 8 SDK for development and build purposes.&#10;- **Dynamic Credentials:** Added dynamic credentials for testing purposes, this should be used with caution.&#10;- **Core Project Update:** The `core` project has been updated to use .NET 8 and all updated packages.&#10;- **Domain Project Update:** The `domain` project has been updated to use .NET 8 and all updated packages.&#10;- **README Update:** The `README.md` file has been updated to reflect the project changes.">
      <option name="closed" value="true" />
      <created>1736677318371</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1736677318371</updated>
    </task>
    <task id="LOCAL-00006" summary="```&#10;feat: update message manager and handlers (file-has-been-updated-to-reflect-the-project-changes-12-01-2025)&#10;&#10;- Refactor MessageManager for constructor injection.&#10;- Update GetMessageById to return nullable Entity.&#10;- Add async validation to handlers.&#10;- Add nullable string properties to responses.&#10;- Add missing null checks.&#10;- Update handler classes for constructor injection.&#10;```">
      <option name="closed" value="true" />
      <created>1736679116589</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1736679116589</updated>
    </task>
    <task id="LOCAL-00007" summary="```&#10;feat: add .idea/ to gitignore (file-has-been-updated-to-reflect-the-project-changes-12-01-2025)&#10;&#10;Added .idea/ directory to .gitignore file to&#10;ignore JetBrains Rider project settings.&#10;```">
      <option name="closed" value="true" />
      <created>1737266707962</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1737266707962</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: introduce optional `ProcedureName` and `ProcedureCode` fields in appointment requests and their validation steps">
      <option name="closed" value="true" />
      <created>1752735282318</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752735282318</updated>
    </task>
    <task id="LOCAL-00009" summary="refactor: cleanup namespace and add async for validation">
      <option name="closed" value="true" />
      <created>1752735336082</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752735336082</updated>
    </task>
    <task id="LOCAL-00010" summary="docs: Update README with detailed information">
      <option name="closed" value="true" />
      <created>1752737606537</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752737606537</updated>
    </task>
    <task id="LOCAL-00011" summary="docs: Add comprehensive API documentation for Cancer Screening SMS system">
      <option name="closed" value="true" />
      <created>1752737631658</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752737631658</updated>
    </task>
    <task id="LOCAL-00012" summary="feat: docs Add initial project documentation&#10;&#10;Adds README, DOCUMENTATION, and QUICK-REFERENCE files.&#10;Provides overview, API details, and setup instructions.">
      <option name="closed" value="true" />
      <created>1752742107799</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752742107799</updated>
    </task>
    <task id="LOCAL-00013">
      <option name="closed" value="true" />
      <created>1752742188575</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752742188575</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="false" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.Identity.Client" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.ApplicationInsights.AspNetCore" />
    <option featureType="dependencySupport" implementationName="executable:az" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.ApplicationInsights" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.Extensions.Logging.ApplicationInsights" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="feat: Refactor appointment controller and add service&#10;&#10;Refactor AppointmentController to use services instead of &#10;MediatR.  Update dependencies to net8.0. Add  &#10;SecurityHeadersMiddleware to improve security. Add model &#10;validation filter.  Improve configuration handling for &#10;users. (18-11-2024)" />
    <MESSAGE value="Add Dynamics integration with repository and service layers&#10;&#10;- Introduce `DynamicsConfig` class for configuration management.&#10;- Implement `DynamicsAppointmentRepository`, `DynamicsLocationRepository`, and `DynamicsPatientRepository` for data access.&#10;- Define repository interfaces: `IAppointmentRepository`, `ILocationRepository`, and `IPatientRepository`.&#10;- Add `RepositoryServiceExtensions` for dependency injection setup.&#10;- Create `ServiceCollectionExtensions` to configure Dynamics client and repositories.&#10;- Develop `AppointmentService` in `Domain.Services` for appointment management.&#10;- Add domain models for appointment requests and responses." />
    <MESSAGE value="Add query to filter by cancer screening in DynamicsLocationRepository and update Dynamics config settings" />
    <MESSAGE value="Refactor DynamicsLocationRepository to use ServiceClient for data retrieval" />
    <MESSAGE value="```&#10;feat: upgrade to .net 8, update packages (main)&#10;&#10;- Updated target framework to net8.0&#10;- Updated package references to latest versions&#10;- Added global.json to enforce .net 8 sdk version&#10;- Added dynamics credentials for testing&#10;- updated core project to use .net 8 and updated&#10;  packages&#10;- updated domain project to use .net 8 and updated&#10;  packages&#10;- Updated readme file&#10;```" />
    <MESSAGE value="- **Package Updates:** All project's package references have been updated to their latest available versions.&#10;- **.NET SDK Enforcement:** A `global.json` file has been added to the root directory to enforce the use of the .NET 8 SDK for development and build purposes.&#10;- **Dynamic Credentials:** Added dynamic credentials for testing purposes, this should be used with caution.&#10;- **Core Project Update:** The `core` project has been updated to use .NET 8 and all updated packages.&#10;- **Domain Project Update:** The `domain` project has been updated to use .NET 8 and all updated packages.&#10;- **README Update:** The `README.md` file has been updated to reflect the project changes." />
    <MESSAGE value="```&#10;feat: update message manager and handlers (file-has-been-updated-to-reflect-the-project-changes-12-01-2025)&#10;&#10;- Refactor MessageManager for constructor injection.&#10;- Update GetMessageById to return nullable Entity.&#10;- Add async validation to handlers.&#10;- Add nullable string properties to responses.&#10;- Add missing null checks.&#10;- Update handler classes for constructor injection.&#10;```" />
    <MESSAGE value="```&#10;feat: add .idea/ to gitignore (file-has-been-updated-to-reflect-the-project-changes-12-01-2025)&#10;&#10;Added .idea/ directory to .gitignore file to&#10;ignore JetBrains Rider project settings.&#10;```" />
    <MESSAGE value="[#3655] " />
    <MESSAGE value="feat: introduce optional `ProcedureName` and `ProcedureCode` fields in appointment requests and their validation steps" />
    <MESSAGE value="refactor: cleanup namespace and add async for validation" />
    <MESSAGE value="docs: Update README with detailed information" />
    <MESSAGE value="docs: Add comprehensive API documentation for Cancer Screening SMS system" />
    <MESSAGE value="feat: docs Add initial project documentation&#10;&#10;Adds README, DOCUMENTATION, and QUICK-REFERENCE files.&#10;Provides overview, API details, and setup instructions." />
    <MESSAGE value="chore: Update .gitignore to exclude IDE files" />
    <MESSAGE value="chore: Update .gitignore to exclude IDE files&#10;" />
    <option name="LAST_COMMIT_MESSAGE" value="" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>
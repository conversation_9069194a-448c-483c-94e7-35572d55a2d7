using Core.Results;
using Domain.Features.Message.Commands.NewAppointment;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Integration;

public class AppointmentIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public AppointmentIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string>
                {
                    ["Users:0:Id"] = "863183B2-A908-4B60-B705-EEB3078288B2",
                    ["Users:0:Name"] = "Test",
                    ["Users:0:ClientId"] = "test",
                    ["Users:0:SecretKey"] = "test",
                    ["Users:0:IsActive"] = "true",
                    ["Dynamics:OrganizationURI"] = "https://test.crm.dynamics.com",
                    ["Dynamics:ClientId"] = "test-client-id",
                    ["Dynamics:SecretKey"] = "test-secret-key"
                }!);
            });
            
            builder.ConfigureServices(services =>
            {
                // Replace real Dynamics service with mock for integration tests
                // This would typically involve removing the real ServiceClient registration
                // and adding a mock implementation
            });
        });

        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task NewAppointment_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task NewAppointment_WithInvalidAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        _client.DefaultRequestHeaders.Add("X-Client-Id", "invalid");
        _client.DefaultRequestHeaders.Add("X-Secret-Key", "invalid");

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task NewAppointment_WithValidAuthentication_ShouldReturnCreated()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        AddAuthenticationHeaders();

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
    }

    [Fact]
    public async Task NewAppointment_WithInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidRequest = new NewAppointmentRequest
        {
            CustomIdentifier = "INVALID", // Should start with HC and be 10 chars
            AppointmentDateTime = DateTime.Now.AddDays(-1) // Should be in future
        };

        var json = JsonSerializer.Serialize(invalidRequest, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        AddAuthenticationHeaders();

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var responseContent = await response.Content.ReadAsStringAsync();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseContent, _jsonOptions);
        
        errorResult.Should().NotBeNull();
        errorResult!.ErrorType.Should().Be((int)APIErrorType.ValidationError);
    }

    [Fact]
    public async Task AllEndpoints_WithValidAuthentication_ShouldBeAccessible()
    {
        // Arrange
        AddAuthenticationHeaders();

        var endpoints = new[]
        {
            "/api/appointment/new",
            "/api/appointment/reschedule",
            "/api/appointment/cancel",
            "/api/appointment/negative-result",
            "/api/appointment/no-show"
        };

        // Act & Assert
        foreach (var endpoint in endpoints)
        {
            var request = CreateValidRequestForEndpoint(endpoint);
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _client.PostAsync(endpoint, content);

            // Should not return 404 (endpoint exists) or 401 (authentication works)
            response.StatusCode.Should().NotBe(HttpStatusCode.NotFound);
            response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        }
    }

    [Fact]
    public async Task API_ShouldReturnCorrectContentType()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        AddAuthenticationHeaders();

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
    }

    [Fact]
    public async Task API_WithMalformedJson_ShouldReturnBadRequest()
    {
        // Arrange
        var malformedJson = "{ invalid json }";
        var content = new StringContent(malformedJson, Encoding.UTF8, "application/json");

        AddAuthenticationHeaders();

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task API_WithEmptyBody_ShouldReturnBadRequest()
    {
        // Arrange
        var content = new StringContent("", Encoding.UTF8, "application/json");

        AddAuthenticationHeaders();

        // Act
        var response = await _client.PostAsync("/api/appointment/new", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("GET")]
    [InlineData("PUT")]
    [InlineData("DELETE")]
    [InlineData("PATCH")]
    public async Task API_WithUnsupportedHttpMethods_ShouldReturnMethodNotAllowed(string httpMethod)
    {
        // Arrange
        AddAuthenticationHeaders();
        var request = new HttpRequestMessage(new HttpMethod(httpMethod), "/api/appointment/new");

        // Act
        var response = await _client.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
    }

    private void AddAuthenticationHeaders()
    {
        _client.DefaultRequestHeaders.Clear();
        _client.DefaultRequestHeaders.Add("X-Client-Id", "test");
        _client.DefaultRequestHeaders.Add("X-Secret-Key", "test");
    }

    private NewAppointmentRequest CreateValidNewAppointmentRequest()
    {
        return new NewAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24,
            ProcedureName = "Mammography",
            ProcedureCode = "MAM001"
        };
    }

    private object CreateValidRequestForEndpoint(string endpoint)
    {
        return endpoint switch
        {
            "/api/appointment/new" => CreateValidNewAppointmentRequest(),
            "/api/appointment/reschedule" => CreateValidNewAppointmentRequest(), // Same structure
            "/api/appointment/cancel" => CreateValidNewAppointmentRequest(), // Same structure
            "/api/appointment/negative-result" => new
            {
                CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
                SubjectIdentifier = "SUBJ-123",
                RecipientFirstName = "John",
                RecipientLastName = "Doe",
                RecipientMobileNumber = "+97412345678",
                RecipientNationality = "Qatari"
            },
            "/api/appointment/no-show" => new
            {
                AppointmentDateTime = TestHelper.GetTestDateTime(),
                AppointmentArriveMinutesBefore = 30,
                CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
                SubjectIdentifier = "SUBJ-123",
                RecipientFirstName = "John",
                RecipientLastName = "Doe",
                RecipientMobileNumber = "+97412345678",
                RecipientNationality = "Qatari"
            },
            _ => throw new ArgumentException($"Unknown endpoint: {endpoint}")
        };
    }
}
